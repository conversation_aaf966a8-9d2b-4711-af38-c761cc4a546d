<?php

namespace app\applent\logic\user;

use app\common\logic\BaseLogic;
use think\facade\Cache;
use app\common\model\user\User;
use app\common\model\user\UserSession;
use app\common\service\tim\TencentImService;
use app\applent\service\UserTokenService;
use app\applent\service\UserLoginService;
use app\common\cache\UserTokenCache;
use think\facade\{Db, Config};
use app\common\service\wechat\WeChatRequestService;
use app\applent\service\WechatUserService;
use app\common\enum\user\UserTerminalEnum;
use app\common\service\FileService;
use app\common\service\ConfigService;
use app\common\model\sms\SmsCode;
/**
 * 登录逻辑
 * Class SmsLogic
 * @package app\api\logic
 */
class LoginLogic extends BaseLogic
{
    /**
     * @notes 发送验证码
     * @param $params
     * @return false|mixed
     */
    public static function sendCode($params)
    {
        try {
            // 使用公共方法解密并验证数据
            $decryptedData = self::decryptAndValidateData($params['encrypted_data']);
            $mobile = $decryptedData['mobile'];

            $ip = request()->ip();

            // 获取配置的每日限制次数
            $smsCodeIp = ConfigService::get('systemconfig', 'sms_code_ip', 0); // IP每日限制
            $smsCodeNum = ConfigService::get('systemconfig', 'sms_code_num', 0); // 手机号每日限制

            // 检查IP每日请求次数限制
            if ($smsCodeIp > 0) {
                $ipCount = self::getTodaySmsCount('ip', $ip);
                if ($ipCount >= $smsCodeIp) {
                    throw new \Exception('该IP今日发送次数已达上限');
                }
            }

            $mobileCount = self::getTodaySmsCount('mobile', $mobile);
            // 检查手机号每日请求次数限制
            if ($smsCodeNum > 0) {
                if ($mobileCount >= $smsCodeNum) {
                    throw new \Exception('该手机号今日发送次数已达上限');
                }
            }

            // 缓存键定义
            $cacheKey = 'sms_code:' . $mobile;
            $timerKey = $cacheKey . '_timer';

            // 60秒限制检查
            if (Cache::has($timerKey)) {
                throw new \Exception('60秒内只能发送一次');
            }

            // 生成6位验证码
            $code = mt_rand(100000, 999999);
            $expireSeconds = 300; // 5分钟过期

            $isSms = ConfigService::get('systemconfig', 'is_sms');
            $sms_engine = ConfigService::get('sms', 'engine');
            $sms_config = ConfigService::get('sms', $sms_engine);
            // 初始化发送状态和结果
            $status = 1; // 默认成功
            $result = '';
            $content = "【" . $sms_config['name'] . "】您的验证码是" . $code . "。如非本人操作，请忽略本短信";
            $sms_engine = 'local';

            //查找配置信息是否开启短信
            
            if($isSms == 1){
                //发送短信
                $smsDriver = new \app\common\service\sms\SmsDriver();
                if (!$smsDriver->initialize()) {
                    throw new \Exception('短信服务初始化失败：' . $smsDriver->getError());
                }
                // 构建短信数据
                $smsData = [
                    'template_id' => $sms_config['secret_id'] ? $sms_config['secret_id'] : '',
                    'params' => [
                        'code' => $code
                    ],
                    'content'=>$content,
                ];

                // 发送短信
                $smsResult = $smsDriver->send($mobile, $smsData);
                if ($smsResult) {
                    $status = 1; // 成功
                    $result = is_array($smsResult) ? json_encode($smsResult, JSON_UNESCAPED_UNICODE) : (string)$smsResult;
                } else {
                    $status = 2; // 失败
                    $result = $smsDriver->getError() ?: '短信发送失败';
                    throw new \Exception('短信发送失败：' . $result);
                }
            }

            // 存储到数据库
            SmsCode::create([
                'mobile'      => $mobile,
                'code'        => $code,
                'scene'       => $params['scene'],
                'ip'          => $ip,
                'operators'   => $sms_engine,
                'times'       => $mobileCount + 1, // 当前次数+1
                'status'      => $status,
                'result'      => $result,
                'content'     => $content,
                'create_time' => time(),
                'expire_time' => time() + $expireSeconds
            ]);

            // 设置60秒计时锁
            Cache::set($timerKey, 1, 60);

            if($isSms == 1){
                $code = '';
            }
            return ['code'=>$code];
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 手机号验证码登录
     * @param $params
     * @return array|false
     */
    public static function loginByCode($params)
    {
        try {
            // 使用公共方法解密并验证数据
            $decryptedData = self::decryptAndValidateData($params['sign']);
            $params['mobile'] = $decryptedData['mobile'];

            // 验证码校验
            $codeRecord = SmsCode::where(['mobile'=>$params['mobile'],'code'=>$params['code']])
            ->order('id', 'desc')
            ->find();   
            if(!$codeRecord){
                throw new \Exception('验证码错误');
            }
            if (time() > $codeRecord['expire_time']) {
                throw new \Exception('验证码已过期');
            }
            $params['ip'] = request()->ip();
            $userInfo = UserLoginService::findOrCreateUser($params);
            return $userInfo;
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 手机号密码注册
     * @param $params
     * @return array|false
     */
    public static function registerByCode($params)
    {
        try {
            // 使用公共方法解密并验证数据
            $decryptedData = self::decryptAndValidateData($params['sign']);
            $params['mobile'] = $decryptedData['mobile'];
            
            //用户是否存在
            $user = User::where('mobile', $params['mobile'])->where(['delete_time'=>null])->findOrEmpty();
            if(!$user->isEmpty()){
                throw new \Exception('用户已存在');
            }
            // // 验证码校验
            $codeRecord = SmsCode::where(['mobile'=>$params['mobile'],'code'=>$params['code']])
            ->order('id', 'desc')
            ->find();   
            if(!$codeRecord){
                throw new \Exception('验证码错误');
            }
            if (time() > $codeRecord['expire_time']) {
                throw new \Exception('验证码已过期');
            }
            $params['ip'] = request()->ip();
            $passwordSalt = Config::get('project.unique_identification');
            $inputPassword = create_password($params['password'], $passwordSalt);
            $params['password'] = $inputPassword;
  
            $user_id = UserLoginService::createUser($params);

            // 获取IM基础配置
            $imConfig = get_im_config($user_id);

            //生成用户访问令牌
            $userInfo = UserTokenService::setToken($user_id, $params['terminal']);

            return [
                'user_info' => $userInfo,
                'im_config' => $imConfig,
            ];
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 手机号密码登录
     * @param $params
     * @return array|false
     */
    public static function loginByMobile($params)
    {
        try {
            $decryptedData = self::decryptAndValidateData($params['sign']);
            $params['mobile'] = $decryptedData['mobile'];

            $params['ip'] = request()->ip();
            $userInfo = UserLoginService::findOrCreateUser($params);
            return $userInfo;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * @notes 微信授权登录
     * @param $params
     * @return array|false
     */
    public static function loginByWechat($params)
    {
        Db::startTrans();
        try {
            // 通过code 获取 access_token,openid,unionid等信息
            $userAuth = WeChatRequestService::getUserAuthByCode($params['code']);

            if (empty($userAuth['openid']) || empty($userAuth['access_token'])) {
                throw new \Exception('获取用户授权信息失败');
            }

            // 获取微信用户信息
            $response = WeChatRequestService::getUserInfoByAuth($userAuth['access_token'], $userAuth['openid']);

            // 生成用户或更新用户信息
            $userServer = new WechatUserService($response, UserTerminalEnum::APP);
            $userInfo = $userServer->getResopnseByUserInfo()->authUserLogin()->getUserInfo();

            // 更新登录信息
            $params['ip'] = request()->ip();
            UserLoginService::updateLoginInfo($userInfo['id'],$params);

            Db::commit();

            //获取IM配置
            $imConfig = get_im_config($userInfo['id']);
            return [
                'user_info' => $userInfo,
                'im_config' => $imConfig
            ];

        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }
    /**
     * @notes 完善用户信息
     * @param $params
     * @return array|false
     */
    public static function perfectInfo(array $params, int $userId)
    {
        try {
            //查找用户是否存在
            $user = User::where('id', $userId)->findOrEmpty();
            if($user->isEmpty()){
                throw new \Exception('用户不存在');
            }
            //通过服务类更新用户资料（包含数据库和IM信息更新）
            $userTokenCache = new UserTokenCache();
            $token = UserLoginService::updateUserInfo($userId,$params);

            //缓存更新后的用户信息
            $userInfo = $userTokenCache->setUserInfo($token);

            //获取IM配置
            $imConfig = get_im_config($userId);
            return [
                'user_info' => $userInfo,
                'im_config' => $imConfig
            ];
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 验证验证码
     * @param array $params
     * @param int $userId
     * @return array|false
     */
    public static function verifyCode(array $params, int $userId)
    {
        try {
            // 使用公共方法解密并验证数据
            $decryptedData = self::decryptAndValidateData($params['sign']);
            $mobile = $decryptedData['mobile'];

            // 验证用户是否存在且手机号匹配
            $user = User::where(['id' => $userId, 'mobile' => $mobile])->findOrEmpty();

            if ($user->isEmpty()) {
                throw new \Exception('用户不存在或手机号不匹配');
            }

            // 验证码校验
            $codeRecord = SmsCode::where(['mobile' => $mobile, 'code' => $params['code']])
                                ->order('id', 'desc')
                                ->find();

            if (!$codeRecord) {
                throw new \Exception('验证码错误');
            }

            // 检查验证码是否过期
            if (time() > $codeRecord['expire_time']) {
                throw new \Exception('验证码已过期');
            }

            return true;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    /**
     * @notes 修改密码
     * @param array $params
     * @param int $userId
     * @return array|false
     */
    public static function changePassword(array $params, int $userId)
    {
        try {
            // 使用公共方法解密并验证数据
            $decryptedData = self::decryptAndValidateData($params['sign']);
            $mobile = $decryptedData['mobile'];

            // 验证用户是否存在且手机号匹配
            $user = User::where(['id' => $userId, 'mobile' => $mobile])->findOrEmpty();

            if ($user->isEmpty()) {
                throw new \Exception('用户不存在或手机号不匹配');
            }

            // 加密新密码
            $passwordSalt = Config::get('project.unique_identification');
            $newPassword = create_password($params['password'], $passwordSalt);

            // 更新密码
            $user->password = $newPassword;
            $user->update_time = time();
            $user->save();

            return true;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }
    
     /* @notes 注销账号
     * @param array $params
     * @param int $userId
     * @return array|false
     */
    public static function logoutAccount(array $params, array $userInfo)
    {
        try {
            // 使用公共方法解密并验证数据
            $decryptedData = self::decryptAndValidateData($params['sign']);
            $mobile = $decryptedData['mobile'];

            // 验证用户是否存在且手机号匹配
            $user = User::where(['id' => $userInfo['id'], 'mobile' => $mobile])->findOrEmpty();

            if ($user->isEmpty()) {
                throw new \Exception('用户不存在或手机号不匹配');
            }

            // 验证码校验
            $codeRecord = SmsCode::where(['mobile' => $mobile, 'code' => $params['code']])
                                ->order('id', 'desc')
                                ->find();
                                
            if (!$codeRecord) {
                throw new \Exception('验证码错误');
            }

            // 检查验证码是否过期
            if (time() > $codeRecord['expire_time']) {
                throw new \Exception('验证码已过期');
            }

            // 注销账号
            $user->delete_time = time();
            $user->delete_id = $userInfo['id'];
            $user->save();

            //注销token
            UserTokenService::expireToken($userInfo['token']);

            return true;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 解密并验证加密数据（用于短信发送）
     * @param string $encryptedData 加密的数据
     * @return array 返回 ['mobile' => '手机号', 'time' => '时间戳']
     * @throws \Exception
     */
    private static function decryptAndValidateData($encryptedData)
    {
        $decryptedData = decrypt($encryptedData);
        if (!$decryptedData) {
            throw new \Exception('非法请求');
        }
        // #分割
        $parts = explode('#', $decryptedData);
        if (count($parts) !== 2) {
            throw new \Exception('数据格式错误');
        }

        $time = $parts[0];   // 时间戳
        $mobile = $parts[1]; // 手机号

        if (!is_numeric($time) || $time > time()) {
            throw new \Exception('非法请求');
        }

        if (time() - $time > 300) {
            throw new \Exception('请求超时');
        }

        return [
            'mobile' => $mobile,
            'time' => $time
        ];
    }


    /**
     * @notes 获取今日短信发送次数
     * @param string $type 类型：ip 或 mobile
     * @param string $value IP地址或手机号
     * @return int
     */
    private static function getTodaySmsCount($type, $value)
    {
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        $where = [
            ['create_time', '>=', $todayStart],
            ['create_time', '<=', $todayEnd]
        ];

        if ($type === 'ip') {
            $where[] = ['ip', '=', $value];
        } elseif ($type === 'mobile') {
            $where[] = ['mobile', '=', $value];
        }

        return SmsCode::where($where)->count();
    }

    /**
     * @notes 退出登录
     * @param $userInfo
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/9/16 17:56
     */
    public static function logout($userInfo)
    {
        //token不存在，不注销
        if (!isset($userInfo['token'])) {
            return false;
        }

        //设置token过期
        return UserTokenService::expireToken($userInfo['token']);
    }
}
