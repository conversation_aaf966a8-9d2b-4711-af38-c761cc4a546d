<?php

namespace app\applent\logic\gift;

use app\common\logic\BaseLogic;
use app\common\model\gift\Gift;
use app\common\model\gift\GiftRecord;
use app\common\model\user\User;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;
use app\common\service\ConfigService;
use think\facade\Db;
use think\facade\Log;
use app\common\model\user\UserRebateRatio;
/**
 * 礼物业务逻辑类
 */
class GiftLogic extends BaseLogic
{

    /**
     * @notes 赠送礼物
     * @param array $params 参数
     * @return array|bool
     */
    public static function sendGift($params)
    {
        // 开启事务
        Db::startTrans();
        try {
            //基础验证
            if ($params['user_id'] == $params['to_user_id']) {
                throw new \Exception('不能给自己赠送礼物');
            }

            //获取礼物信息
            $gift = Gift::find($params['gift_id']);
            if (!$gift) {
                throw new \Exception('礼物不存在');
            }

            //获取收礼用户信息
            $toUser = User::field('id,sex,is_auth,nickname,avatar,is_open_gift_commission')->find($params['to_user_id'])->toArray();
            if (empty($toUser)) {
                throw new \Exception('用户不存在');
            }
            //同性之间无法赠送礼物
            if($params['sex'] == $toUser['sex']){
                throw new \Exception('同性之间无法赠送礼物');
            }
            //收礼方女性且未认证，无法赠送礼物
            if($toUser['sex'] == 2 && $toUser['is_auth'] != 1){
                throw new \Exception('对方未认证，无法赠送礼物');
            }
            //计算总金额
            $totalAmount = $gift->coin * $params['gift_count'];

            //生成订单号
            $orderNo = order_sn('GIFT');

            //获取赠送用户当前余额
            $senderBalance = UserBalance::getUserBalance($params['user_id']);
            $beforeSenderBalance = $senderBalance ? $senderBalance['balance'] : 0;

            //检查用户余额
            if ($beforeSenderBalance < $totalAmount) {
                throw new \Exception('余额不足，请先充值');
            }

            //扣除赠送用户余额
            $deductResult = UserBalance::subBalance($params['user_id'], $totalAmount,$totalAmount,$totalAmount);
            if (!$deductResult) {
                throw new \Exception('余额扣除失败');
            }

            //记录赠送用户余额变动日志
            $afterSenderBalance = $beforeSenderBalance - $totalAmount;
            $senderLogResult = UserCoinLog::addGiftLog(
                $params['user_id'],
                $beforeSenderBalance,
                $afterSenderBalance,
                -$totalAmount,
                $orderNo,
                $params['to_user_id'],
                $gift->name,
                $params['gift_count'],
                $gift->coin,
            );

            if (!$senderLogResult) {
                throw new \Exception('赠送用户余额变动日志记录失败');
            }

            //计算接收用户收益（根据用户设置或系统配置计算抽成）
            $commissionRate = self::calculateCommissionRate($toUser);
            $actualIncome = round($totalAmount * (100 - $commissionRate) / 100, 2);
        
            //获取接收用户当前收益
            $receiverBalance = UserBalance::getUserBalance($params['to_user_id']);
            $beforeReceiverIncome = $receiverBalance ? $receiverBalance['income'] : 0;

            //增加接收用户收益
            $addIncomeResult = UserBalance::addGiftIncome($params['to_user_id'], $actualIncome);
            if (!$addIncomeResult) {
                throw new \Exception('接收用户收益增加失败');
            }

            //记录接收用户收益变动日志
            $afterReceiverIncome = $beforeReceiverIncome + $actualIncome;
            $receiverLogResult = UserIncomeLog::addGiftReceiveLog(
                $params['user_id'],
                $beforeReceiverIncome,
                $afterReceiverIncome,
                $actualIncome,
                $orderNo,
                $params['to_user_id'],
                $gift->name,
                $params['gift_count'],
                $commissionRate,
                $totalAmount,
                $gift->coin
            );

            if (!$receiverLogResult) {
                throw new \Exception('接收用户收益变动日志记录失败');
            }

            //创建礼物记录
            $giftLogData = [
                'user_id'           => $params['user_id'],
                'nickname'          => $params['nickname'],
                'to_user_id'        => $params['to_user_id'],
                'to_nickname'       => $toUser['nickname'],
                'gift_id'           => $params['gift_id'],
                'gift_img'          => $gift->img,
                'gift_name'         => $gift->name,
                'gift_count'        => $params['gift_count'],
                'gift_coin'         => $gift->coin,
                'total_amount'      => $totalAmount,
                'commission_rate'   => $commissionRate,
                'actual_income'     => $actualIncome,
                'source_type'       => $params['source_type'] ?? 1,
                'source_id'         => $params['source_id'] ?? '',
                'is_global'         => $gift->is_all_notify ?? 0,
                'order_no'          => $orderNo,
                'update_time'       => time(),
                'create_time'       => time(),
            ];

            $giftRecordId = GiftRecord::insertGetId($giftLogData);

            //处理返佣
            $commissionInfo = calculate_user_commission($params['to_user_id'], $totalAmount, 2); // 类型2=礼物
            if (!empty($commissionInfo)) {
                self::handleCommissionReward($commissionInfo, $orderNo, $params['to_user_id'], $totalAmount);
            }

            // 全局广播
            if ($gift->is_all_notify == 1) {
               
                $senderInfo = [
                    'id'            => $params['user_id'],
                    'nickname'      => $params['nickname'],
                    'avatar'        => $params['avatar'],
                ];
                send_global_gift_message(
                    $senderInfo,
                    $toUser,
                    $params['gift_count'],
                    $gift->name,
                    $gift->img,
                    $gift->svga
                );
            }

            // 提交事务
            Db::commit();

            return [
                'order_no'  => $orderNo,
                'gift_name' => $gift->name,
                'svga'      => $gift->svga,
            ];

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 处理返佣奖励
     * @param array $commissionInfo 返佣信息
     * @param string $orderNo 订单号
     * @param int $toUserId 接收用户ID
     * @return bool
     * @throws \Exception
     */
    private static function handleCommissionReward($commissionInfo, $orderNo, $toUserId, $totalAmount)
    {
        try {
            foreach ($commissionInfo as $level => $info) {
                if (empty($info['user_id']) || empty($info['commission'])) {
                    continue;
                }

                $inviterId = $info['user_id'];
                $commissionAmount = $info['commission'];
                $commissionRate = $info['rate'];
                $levelNum = $level == 'level1' ? 1 : 2;

                // 获取邀请人当前收益
                $inviterBalance = UserBalance::getUserBalance($inviterId);
                $beforeIncome = $inviterBalance ? $inviterBalance['income'] : 0;

                // 增加邀请人收益
                $addIncomeResult = UserBalance::addCommissionIncome($inviterId, $commissionAmount);
                if (!$addIncomeResult) {
                    throw new \Exception('邀请人收益增加失败');
                }

                // 记录收益变动日志
                $afterIncome = $beforeIncome + $commissionAmount;
                $incomeLogResult = UserIncomeLog::addGiftCommissionLog(
                    $inviterId,
                    $beforeIncome,
                    $afterIncome,
                    $commissionAmount,
                    $levelNum,
                    $orderNo,
                    $toUserId,
                    $commissionRate,
                    $totalAmount 
                );

                if (!$incomeLogResult) {
                    throw new \Exception('收益变动日志记录失败');
                }
            }

            return true;
        } catch (\Exception $e) {
            // 返佣失败不影响主流程，只记录日志
            Log::error('礼物返佣处理失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 计算用户礼物抽成比例
     * @param array $toUser 收礼用户信息
     * @return float 抽成比例
     */
    private static function calculateCommissionRate($toUser)
    {
        // 检查用户是否开启个人抽成设置
        if ($toUser['is_open_gift_commission'] == 1) {
            // 从用户返佣比例表获取
            $userRebate = UserRebateRatio::where('user_id', $toUser['id'])->find();
            if ($userRebate && isset($userRebate->gift_commission)) {
                return floatval($userRebate->gift_commission);
            }
        }

        // 使用系统默认配置
        return floatval(ConfigService::get('systemconfig', 'gift_commission', 50));
    }
}
