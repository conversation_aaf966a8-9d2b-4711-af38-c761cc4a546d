<?php

namespace app\applent\logic\recharge;

use app\common\logic\BaseLogic;
use app\common\model\recharge\RechargePackage;
use app\common\model\recharge\RechargeOrder;
use app\common\model\user\User;
use app\common\model\user\UserBalance;
use app\common\model\user\UserCoinLog;
use app\common\model\user\UserIncomeLog;
use app\common\model\user\CommissionIncome;
use app\common\model\user\UserInviteRecord;
use app\common\service\ConfigService;
use app\common\service\pay\PayDriver;
use app\common\enum\pay\PayEnum;
use think\facade\Db;
use think\facade\Cache;
use app\common\model\pay\PayConfig;

/**
 * 充值业务逻辑
 * Class RechargeLogic
 * @package app\applent\logic\recharge
 */
class RechargeLogic extends BaseLogic
{
    /**
     * @notes 获取充值套餐列表
     * @param array $params
     * @return array|false
     */
    public static function getPackageList()
    {
        return RechargePackage::field('id,coin_amount,bonus_coin,price,icon')->order('sort desc')->select()->toArray();
    }
    /**
     * @notes 获取启用的支付方式列表
     */
    public static function getEnabledPayMethods()
    {
        return PayConfig::where(['status' => 1])->field('id,name,icon,pay_way')->select()->toArray();
    }
    /**
     * @notes 创建充值订单并直接调起支付
     * @param int $userId 用户ID
     * @param array $params 参数
     * @return array|false
     */
    public static function createOrder($userId, $params)
    {
        try {
            // 检查用户是否存在
            $user = User::where(['id' => $userId])->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }

            // 检查IP限制（同一个IP当日不能超过100次订单）
            $clientIp = request()->ip();
            //查询当日数据
            $today = date('Y-m-d');
            $ipOrderCount = RechargeOrder::whereDay('create_time', $today)->where(['ip' => $clientIp])->count();
            if ($ipOrderCount >= 100) {
                throw new \Exception('当日订单创建次数已达上限，请明日再试');
            }

            // 检查套餐是否存在
            $package = RechargePackage::where(['id' => $params['package_id']])->find();
            if (!$package) {
                throw new \Exception('充值套餐不存在');
            }

            // 检查频率限制（1分钟内最多创建5次订单）
            $cacheKey = 'recharge_order_limit_' . $userId;
            $orderCount = Cache::get($cacheKey, 0);
            if ($orderCount >= 5) {
                throw new \Exception('创建订单过于频繁，请稍后再试');
            }
            //支付方式
            $payMethod = PayConfig::where(['id' => $params['pay_method_id'],'status' => 1])->find();
            if (empty($payMethod)) {
                throw new \Exception('支付方式不存在');
            }

            // 检查是否为首次充值，获取首冲奖励
            $isFirstOrder = self::checkIsFirstOrder($userId);
            $firstChargeReward = 0;
            if ($isFirstOrder) {
                $firstChargeReward = (int)ConfigService::get('systemconfig', 'first_charge_reward', 0);
            }

            // 创建订单数据
            $orderData = [
                'user_id'               => $userId,
                'order_no'              => order_sn('R'),
                'package_id'            => $params['package_id'],
                'coin_amount'           => $package['coin_amount'],
                'bonus_coin'            => $package['bonus_coin'],
                'first_charge_reward'   => $firstChargeReward, // 首冲赠送金币数量
                'total_coin'            => $package['coin_amount'] + $package['bonus_coin'] + $firstChargeReward,
                'pay_amount'            => $package['price'],
                'pay_method_id'         => $params['pay_method_id'],
                'pay_type'              => $payMethod['pay_way'],
                'pay_status'            => 0,
                'ip'                    => $clientIp,
                'create_time'           => time(),
                'update_time' => time(),
            ];

            // 保存订单
            $order = RechargeOrder::create($orderData);
            if (!$order) {
                static::setError('订单创建失败');
                return false;
            }

            // 更新频率限制缓存（1分钟内计数+1）
            $newCount = $orderCount + 1;
            Cache::set($cacheKey, $newCount, 60);

            // 直接调起支付
            $payResult = self::createPayOrder($order);
            if (!$payResult) {
                // 获取createPayOrder中设置的具体错误信息
                $errorMsg = static::getError() ?: '支付订单创建失败';
                throw new \Exception($errorMsg);
            }

            return array_merge([
                'order_id'      => $order['id'],                    //订单ID
                'order_no'      => $order['order_no'],              //订单号
                'pay_amount'    => $order['pay_amount'].'元',       //支付金额
                'coin_amount'   => $order['coin_amount'].ConfigService::get('systemconfig', 'currency_name', '金币'),           //充值金币数量
                'bonus_coin'    => $order['bonus_coin'].ConfigService::get('systemconfig', 'currency_name', '金币'),            //充值奖励金币数量
                'pay_name'      => $payMethod['name'],              //支付方式名称
                'create_time'   => $order['create_time'],           //订单创建时间
            ], $payResult);

        } catch (\Exception $e) {
            static::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 创建支付订单调起支付
     * @param object $order 订单对象
     * @return array|false
     */
    private static function createPayOrder($order)
    {
        try {
            // 根据支付类型映射到支付引擎
            $payWay = self::mapPayMethodToEngine($order['pay_type']);
            if (!$payWay) {
                throw new \Exception('不支持的支付方式');
            }

            // 获取支付引擎配置
            $payConfig = PayConfig::where([
                'id' => $order['pay_method_id'],
                'status' => 1
            ])->find();
            if (!$payConfig) {
                throw new \Exception('支付渠道已关闭，请重新生成订单');
            }
            $payConfig = json_decode($payConfig['config'], true);
            if (!$payConfig) {
                throw new \Exception('支付渠道配置错误，请重新生成订单');
            }
            // 初始化支付驱动
            $payDriver = new PayDriver($payWay);
            $engine = $payDriver->getEngine($payWay, $payConfig);
            if (!$engine) {
                throw new \Exception('支付引擎初始化失败: ' . $payDriver->getError());
            }

            // 构建支付参数
            $payParams = [
                'out_trade_no'  => $order['order_no'],
                'total_amount'  => $order['pay_amount'],
                'subject'       => '充值' . $order['coin_amount'] . '金币',
                'body'          => '充值' . $order['coin_amount'] . '金币',
                'notify_url'    => self::getNotifyUrl($payWay),
                'return_url'    => self::getReturnUrl($payWay),
                'pay_type'      => self::getPayType($order['pay_type']),
            ];
            // 调用统一下单
            $result = $engine->unifiedOrder($payParams);
            if (!$result) {
                throw new \Exception('支付下单失败: ' . $engine->getError());
            }

            return $result;

        } catch (\Exception $e) {
            static::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 映射支付方式到支付引擎
     * @param int $payType 支付类型 1=微信支付,2=支付宝支付,3和4=小叶支付
     * @return string|false
     */
    private static function mapPayMethodToEngine($payType)
    {
        $mapping = [
            1 => PayEnum::WECHAT_APP,    // 微信支付
            2 => PayEnum::ALIPAY_APP,    // 支付宝支付
            3 => PayEnum::XIAOYE_PAY,    // 小叶支付
            4 => PayEnum::XIAOYE_PAY,    // 小叶支付
        ];

        return $mapping[$payType] ?? false;
    }



    /**
     * @notes 获取支付类型
     * @param int $payType 支付类型 1=微信支付,2=支付宝支付,3和4=小叶支付
     * @return string
     */
    private static function getPayType($payType)
    {
        $mapping = [
            1 => 'wxpay',   // 微信支付
            2 => 'alipay',  // 支付宝支付
            3 => 'wxpay',  // 小叶支付-微信
            4 => 'alipay',   // 小叶支付-支付宝
        ];

        return $mapping[$payType] ?? 'alipay';
    }

    /**
     * @notes 获取支付回调地址
     * @param string $payWay 支付方式
     * @return string
     */
    private static function getNotifyUrl($payWay)
    {
        $baseUrl = request()->domain();

        // 为不同支付方式生成不同的回调地址
        switch ($payWay) {
            case PayEnum::WECHAT_APP:
                return $baseUrl . '/applent/recharge.recharge/wechat_callback';
            case PayEnum::ALIPAY_APP:
                return $baseUrl . '/applent/recharge.recharge/alipay_callback';
            case PayEnum::XIAOYE_PAY:
                return $baseUrl . '/applent/recharge.recharge/xiaoye_callback';
            default:
                return $baseUrl . '/applent/recharge.recharge/pay_callback/' . $payWay;
        }
    }

    /**
     * @notes 获取支付返回地址
     * @param string $payWay 支付方式
     * @return string
     */
    private static function getReturnUrl($payWay = '')
    {
        $baseUrl = request()->domain();

        // 为不同支付方式生成不同的返回地址
        switch ($payWay) {
            case PayEnum::WECHAT_APP:
                return $baseUrl . '/applent/recharge.recharge/wechat_return';
            case PayEnum::ALIPAY_APP:
                return $baseUrl . '/applent/recharge.recharge/alipay_return';
            case PayEnum::XIAOYE_PAY:
                return $baseUrl . '/applent/recharge.recharge/xiaoye_return';
            default:
                return $baseUrl . '/applent/recharge.recharge/pay_return';
        }
    }

    /**
     * @notes 处理支付回调
     * @param array $params 参数
     * @param string $payWay 支付方式
     * @return array|false
     */
    public static function handlePayCallback($params, $payWay = null)
    {
        try {
            Db::startTrans();

            // 小叶支付回调
            if ($payWay == 'xiaoye_pay') {
                if ($params['trade_status'] != 'TRADE_SUCCESS') {
                    Db::rollback();
                    throw new \Exception('支付失败');
                }
                // 查找订单
                $order = RechargeOrder::where(['order_no' => $params['out_trade_no']])->find();
                if (!$order) {
                    Db::rollback();
                    throw new \Exception('订单不存在');
                }
                // 处理回调数据
                $processedParams = self::xiaoyePayCallback($order, $payWay, $params);
                if ($processedParams === false) {
                    Db::rollback();
                    throw new \Exception(static::getError() ?: '支付回调验证失败');
                }
                $params = $processedParams;
            }

            // 检查订单状态
            if ($order['pay_status'] == 2) {
                Db::rollback();
                throw new \Exception('订单已支付，请勿重复处理');
            }

            // 检查用户是否为首次充值
            $isFirstOrder = self::checkIsFirstOrder($order['user_id']);

            // 更新订单状态
            $updateData = [
                'pay_status'        => 2,
                'payinfo'           => json_encode($params),
                'serial_number'     => $params['transaction_id'] ?? $params['trade_no'] ?? '',
                'is_first_order'    => $isFirstOrder ? 1 : 0,
                'update_time'       => time(),
                'pay_time'          => time()
            ];
            $result = RechargeOrder::where(['id' => $order['id']])->update($updateData);
            if (!$result) {
                Db::rollback();
                throw new \Exception('订单状态更新失败');
            }

            // 获取用户当前金币
            $userBalance = UserBalance::getUserBalance($order['user_id']);
            $beforeAmount = $userBalance ? $userBalance['balance'] : 0;

            // 增加用户金币（包含首冲奖励）
            $totalCoinToAdd = $order['total_coin'];
            $addBalanceResult = UserBalance::addBalance($order['user_id'], $totalCoinToAdd,$totalCoinToAdd);
            if (!$addBalanceResult) {
                Db::rollback();
                throw new \Exception('用户金币增加失败');
            }

            // 记录金币变动日志
            $afterAmount = $beforeAmount + $totalCoinToAdd;
            $logResult = UserCoinLog::addRechargeLog(
                $order['user_id'],
                $beforeAmount,
                $afterAmount,
                $totalCoinToAdd,
                $order['order_no']
            );

            if (!$logResult) {
                Db::rollback();
                throw new \Exception('金币变动日志记录失败');
            }

            // 处理返佣信息
            $commissionInfo = calculate_user_commission($order['user_id'], $order['pay_amount'], 1);
            if (!empty($commissionInfo)) {
                self::handleCommissionReward($commissionInfo, $order);
            }

            //如果是首冲，增加奖励
            if ($isFirstOrder) {
                //给上级首冲奖励
                self::handleFirstChargeReward($order);
            }
            Db::commit();

            return true;

        } catch (\Exception $e) {
            Db::rollback();
            static::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 检查是否为首次充值订单
     * @param int $userId 用户ID
     * @return bool
     */
    private static function checkIsFirstOrder($userId)
    {
        // 查询该用户是否有已支付的充值订单
        $count = RechargeOrder::where(['user_id' => $userId, 'pay_status' => 2])->count();

        return $count == 0;
    }

    /**
     * @notes 处理返佣奖励
     * @param array $commissionInfo 返佣信息
     * @param array $order 订单信息
     * @return bool
     * @throws \Exception
     */
    private static function handleCommissionReward($commissionInfo, $order)
    {
        try {
            foreach ($commissionInfo as $level => $info) {
                if (empty($info['user_id']) || empty($info['commission'])) {
                    continue;
                }

                $inviterId = $info['user_id'];
                $commissionAmount = $info['commission'];
                $commissionRate = $info['rate'];
                $levelNum = $level == 'level1' ? 1 : 2;

                // 获取邀请人当前收益
                $inviterBalance = UserBalance::getUserBalance($inviterId);
                $beforeIncome = $inviterBalance ? $inviterBalance['income'] : 0;

                // 增加邀请人收益
                $addIncomeResult = UserBalance::addIncome($inviterId, $commissionAmount,$commissionAmount,$commissionAmount);
                if (!$addIncomeResult) {
                    throw new \Exception('邀请人收益增加失败');
                }

                // 记录收益变动日志
                $afterIncome = $beforeIncome + $commissionAmount;
                // 计算抽成前金额（下级用户充值金额*10）
                $totalIncome = $order['pay_amount'] * 10;
                $incomeLogResult = UserIncomeLog::addRechargeCommissionLog(
                    $inviterId,
                    $beforeIncome,
                    $afterIncome,
                    $commissionAmount,
                    $levelNum,
                    $order['order_no'],
                    $order['user_id'],
                    $commissionRate,
                    $totalIncome
                );

                if (!$incomeLogResult) {
                    throw new \Exception('收益变动日志记录失败');
                }

                // 记录佣金收益明细
                // $commissionResult = CommissionIncome::addRechargeCommission(
                //     $inviterId,
                //     $order['user_id'],
                //     $levelNum,
                //     $commissionAmount,
                //     $commissionRate,
                //     $order['pay_amount'],
                //     $order['order_no']
                // );

                // if (!$commissionResult) {
                //     throw new \Exception('佣金收益记录失败');
                // }
            }

            return true;

        } catch (\Exception $e) {
            throw new \Exception('返佣处理失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 处理首冲额外奖励
     * @param array $order 订单信息
     * @return bool
     * @throws \Exception
     */
    private static function handleFirstChargeReward($order)
    {
        try {
            // 获取首冲额外奖励配置
            $firstChargeReward = ConfigService::get('systemconfig', 'first_charge_higher_reward', 0);

            // 如果没有配置首冲奖励或奖励为0，直接返回
            if (!$firstChargeReward || $firstChargeReward <= 0) {
                return true;
            }

            // 查找用户的上级（邀请人）
            $inviteRecord = UserInviteRecord::where('invite_user_id', $order['user_id'])->find();
            if (!$inviteRecord) {
                return true; // 没有上级，直接返回
            }

            $inviterId = $inviteRecord->user_id;

            // 获取上级当前收益
            $inviterBalance = UserBalance::getUserBalance($inviterId);
            $beforeIncome = $inviterBalance ? $inviterBalance['income'] : 0;

            // 增加上级收益
            $addIncomeResult = UserBalance::addIncome($inviterId, $firstChargeReward,$firstChargeReward,$firstChargeReward);
            if (!$addIncomeResult) {
                throw new \Exception('上级首冲奖励增加失败');
            }

            // 记录收益变动日志
            $afterIncome = $beforeIncome + $firstChargeReward;
            $incomeLogResult = UserIncomeLog::addFirstChargeRewardLog(
                $inviterId,
                $beforeIncome,
                $afterIncome,
                $firstChargeReward,
                $order['order_no'],
                $order['user_id']
            );

            if (!$incomeLogResult) {
                throw new \Exception('首冲奖励收益变动日志记录失败');
            }

            return true;

        } catch (\Exception $e) {
            throw new \Exception('首冲奖励处理失败：' . $e->getMessage());
        }
    }

    /**
     * @notes 小叶支付回调
     * @param object $order 订单对象
     * @param string $payWay 支付方式
     * @param array $params 回调参数
     * @return array|false 处理后的参数，失败返回false
     */
    private static function xiaoyePayCallback($order, $payWay, $params)
    {
        try {
            // 根据pay_method_id查找PayConfig配置
            $payConfigRecord = PayConfig::where([
                'id' => $order['pay_method_id'],
                'status' => 1
            ])->find();

            if (!$payConfigRecord) {
                static::setError('支付配置不存在');
                return false;
            }

            // 解析JSON配置
            $payConfig = json_decode($payConfigRecord['config'], true);
            if (!$payConfig) {
                static::setError('支付配置格式错误');
                return false;
            }

            // 初始化支付驱动
            $payDriver = new PayDriver($payWay);
            $engine = $payDriver->getEngine($payWay, $payConfig);

            if (!$engine) {
                static::setError('支付引擎初始化失败: ' . $payDriver->getError());
                return false;
            }

            // 验证回调签名
            if (!$engine->verifyNotify($params)) {
                static::setError('回调签名验证失败: ' . ($engine->getError() ?: '签名无效'));
                return false;
            }

            // 处理回调数据
            $callbackResult = $engine->handleNotify($params);
            if (!$callbackResult) {
                static::setError('回调处理失败: ' . ($engine->getError() ?: '处理异常'));
                return false;
            }

            // 使用处理后的数据
            return array_merge($params, $callbackResult);

        } catch (\Exception $e) {
            static::setError('支付回调验证异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * @notes 查询订单支付状态（前端轮询）
     * @param array $params
     * @return array|false
     */
    public static function queryOrderStatus($params)
    {
        try {
            return RechargeOrder::where(['id'=>$params['order_id'],'user_id'=>$params['user_id']])->value('pay_status') ?? 3;
        } catch (\Exception $e) {
            static::setError($e->getMessage());
            return false;
        }
    }
}