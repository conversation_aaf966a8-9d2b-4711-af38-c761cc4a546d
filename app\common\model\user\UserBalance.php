<?php

namespace app\common\model\user;

use app\common\model\BaseModel;

/**
 * 用户余额模型
 * Class UserBalance
 * @package app\common\model\user
 */
class UserBalance extends BaseModel
{
    protected $name = 'user_balance';

    /**
     * @notes 获取用户余额信息
     * @param int $userId 用户ID
     * @return UserBalance|null
     */
    public static function getUserBalance($userId)
    {
        return self::where(['user_id' => $userId])->find();
    }

    /**
     * @notes 创建用户余额记录
     * @param int $userId 用户ID
     * @return UserBalance
     */
    public static function createUserBalance($userId)
    {
        return self::create([
            'user_id' => $userId,
            'balance' => 0,
            'income' => 0.00,
            'create_time' => time(),
            'update_time' => time(),
        ]);
    }

    /**
     * @notes 增加用户余额
     * @param int $userId 用户ID
     * @param float $amount 增加金额
     * @param float $recharge_balance 充值金额
     * @return bool
     */
    public static function addBalance($userId, $amount, $recharge_balance = 0)
    {
        // 查找用户余额记录
        $userBalance = self::getUserBalance($userId);

        // 如果不存在则创建
        if (!$userBalance) {
            $userBalance = self::createUserBalance($userId);
        }

        // 确保参数都是数值类型
        $amount = (float)$amount;
        $recharge_balance = (float)$recharge_balance;

        // 增加余额
        return self::where(['user_id' => $userId])
            ->inc('balance', $amount)
            ->inc('recharge_balance', $recharge_balance)
            ->update(['update_time' => time()]);
    }

    /**
     * @notes 减少用户余额
     * @param int $userId 用户ID
     * @param float $amount 减少金额
     * @param float $consume_balance 消费金额
     * @param float $consume_gift_balance 礼物消费金额
     * @param float $consume_video_balance 视频消费金额
     * @return bool
     */
    public static function subBalance($userId, $amount, $consume_balance = 0, $consume_gift_balance = 0, $consume_video_balance = 0)
    {
        // 确保参数都是数值类型
        $amount = (float)$amount;
        $consume_balance = (float)$consume_balance;
        $consume_gift_balance = (float)$consume_gift_balance;
        $consume_video_balance = (float)$consume_video_balance;

        $userBalance = self::getUserBalance($userId);

        if (!$userBalance || $userBalance['balance'] < $amount) {
            return false; // 余额不足
        }

        return self::where(['user_id' => $userId])
            ->dec('balance', $amount)
            ->inc('consume_balance', $consume_balance)
            ->inc('consume_gift_balance', $consume_gift_balance)
            ->inc('consume_video_balance', $consume_video_balance)
            ->update(['update_time' => time()]);
    }

    /**
     * @notes 增加用户收益
     * @param int $userId 用户ID
     * @param float $amount 增加收益
     * @param float $commission_income 佣金收益
     * @param float $total_income 总收益
     * @param float $gift_income 礼物收益
     * @param float $video_income 视频收益
     * @return bool
     */
    public static function addIncome($userId, $amount, $commission_income = 0, $total_income = 0, $gift_income = 0, $video_income = 0)
    {
        // 查找用户余额记录
        $userBalance = self::getUserBalance($userId);

        // 如果不存在则创建
        if (!$userBalance) {
            $userBalance = self::createUserBalance($userId);
        }

        // 确保所有参数都是数值类型
        $amount = (float)$amount;
        $commission_income = (float)$commission_income;
        $total_income = (float)$total_income;
        $gift_income = (float)$gift_income;
        $video_income = (float)$video_income;

        // 增加收益
        return self::where(['user_id' => $userId])
            ->inc('income', $amount)
            ->inc('commission_income', $commission_income)
            ->inc('total_income', $total_income)
            ->inc('gift_income', $gift_income)
            ->inc('video_income', $video_income)
            ->update(['update_time' => time()]);
    }

    /**
     * @notes 增加礼物收益（便捷方法）
     * @param int $userId 用户ID
     * @param float $amount 礼物金额
     * @return bool
     */
    public static function addGiftIncome($userId, $amount)
    {
        return self::addIncome(
            $userId,
            $amount,    // 增加收益
            0,          // 佣金收益
            $amount,    // 总收益
            $amount,    // 礼物收益
            0           // 视频收益
        );
    }

    /**
     * @notes 增加佣金收益（便捷方法）
     * @param int $userId 用户ID
     * @param float $amount 佣金金额
     * @return bool
     */
    public static function addCommissionIncome($userId, $amount)
    {
        return self::addIncome(
            $userId,
            $amount,    // 增加收益
            $amount,    // 佣金收益
            $amount,    // 总收益
            0,          // 礼物收益
            0           // 视频收益
        );
    }

    /**
     * @notes 增加视频收益（便捷方法）
     * @param int $userId 用户ID
     * @param float $amount 视频收益金额
     * @return bool
     */
    public static function addVideoIncome($userId, $amount)
    {
        return self::addIncome(
            $userId,
            $amount,    // 增加收益
            0,          // 佣金收益
            $amount,    // 总收益
            0,          // 礼物收益
            $amount     // 视频收益
        );
    }
}
