<?php

namespace app\common\model\user;

use app\common\model\BaseModel;

/**
 * 用户余额模型
 * Class UserBalance
 * @package app\common\model\user
 */
class UserBalance extends BaseModel
{
    protected $name = 'user_balance';

    /**
     * @notes 获取用户余额信息
     * @param int $userId 用户ID
     * @return UserBalance|null
     */
    public static function getUserBalance($userId)
    {
        return self::where(['user_id' => $userId])->find();
    }

    /**
     * @notes 创建用户余额记录
     * @param int $userId 用户ID
     * @return UserBalance
     */
    public static function createUserBalance($userId)
    {
        return self::create([
            'user_id' => $userId,
            'balance' => 0,
            'income' => 0.00,
            'create_time' => time(),
            'update_time' => time(),
        ]);
    }

    /**
     * @notes 增加用户余额
     * @param int $userId 用户ID
     * @param int $amount 增加金额
     * @param int $recharge_balance 充值金额
     * @return bool
     */
    public static function addBalance($userId, $amount,$recharge_balance = 0)
    {
        // 查找用户余额记录
        $userBalance = self::getUserBalance($userId);
        
        // 如果不存在则创建
        if (!$userBalance) {
            $userBalance = self::createUserBalance($userId);
        }

        // 增加余额
        return self::where(['user_id' => $userId])
            ->inc('balance', $amount)
            ->inc('recharge_balance', $recharge_balance)
            ->update(['update_time' => time()]);
    }

    /**
     * @notes 减少用户余额
     * @param int $userId 用户ID
     * @param int $amount 减少金额
     * @param int $consume_balance 消费金额
     * @param int $consume_gift_balance 礼物消费金额
     * @param int $consume_video_balance 视频消费金额
     * @return bool
     */
    public static function subBalance($userId, $amount,$consume_balance=0,$consume_gift_balance=0,$consume_video_balance=0)
    {
        $userBalance = self::getUserBalance($userId);
        
        if (!$userBalance || $userBalance['balance'] < $amount) {
            return false; // 余额不足
        }

        return self::where(['user_id' => $userId])
            ->dec('balance', $amount)
            ->inc('consume_balance', $consume_balance)
            ->inc('consume_gift_balance', $consume_gift_balance)
            ->inc('consume_video_balance', $consume_video_balance)
            ->update(['update_time' => time()]);
    }

    /**
     * @notes 增加用户收益
     * @param int $userId 用户ID
     * @param float $amount 增加收益
     * @param float $commission_income 佣金收益
     * @param float $total_income 总收益
     * @param float $gift_income 礼物收益
     * @param float $video_income 视频收益
     * @return bool
     */
    public static function addIncome($userId, $amount,$commission_income=0,$total_income=0,$gift_income=0,$video_income=0)
    {
        // 查找用户余额记录
        $userBalance = self::getUserBalance($userId);
        
        // 如果不存在则创建
        if (!$userBalance) {
            $userBalance = self::createUserBalance($userId);
        }

        // 增加收益
        return self::where(['user_id' => $userId])
            ->inc('income', $amount)
            ->inc('commission_income', $commission_income)
            ->inc('total_income', $total_income)
            ->inc('gift_income', $gift_income)
            ->inc('video_income', $video_income)
            ->update(['update_time' => time()]);
    }
}
