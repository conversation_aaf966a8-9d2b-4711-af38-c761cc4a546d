<?php

namespace app\applent\controller\video;

use app\applent\controller\BaseApiController;
use app\applent\logic\video\VideoCallLogic;
use app\applent\validate\video\VideoCallValidate;
use think\facade\Cache;

/**
 * 视频通话控制器
 */
class VideoController extends BaseApiController
{
    /**
     * 发起通话
     */
    public function start_call()
    {
        $a = nickname();
        var_dump($a);die;
        $params = (new VideoCallValidate())->post()->goCheck('start_call');
        $params['user_id'] = $this->userId;
        $result = VideoCallLogic::startCall($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }
        return $this->success('通话发起成功', $result,0,1);
    }

    /**
     * 接通通话
     * @return \think\response\Json
     */
    public function answer_call()
    {
        $params = (new VideoCallValidate())->post()->goCheck('answer_call');
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::answerCall($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }
        return $this->success('接通成功', [],1,0);
    }

    /**
     * 实时扣费API（前端每5秒轮询）
     * @return \think\response\Json
     */
    public function real_time_charge()
    {
        $params = (new VideoCallValidate())->post()->goCheck('real_time_charge');
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::realTimeCharge($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }
        return $this->success('扣费检查完成', $result);
    }

    /**
     * 挂断通话
     * @return \think\response\Json
     */
    public function hangup_call()
    {
        $params = (new VideoCallValidate())->post()->goCheck('hangup_call');
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::hangupCall($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }
        return $this->success('挂断成功', [],1,1);
    }

    /**
     * 结束通话
     * @return \think\response\Json
     */
    public function end_call()
    {
        $params = (new VideoCallValidate())->post()->goCheck('end_call');
        $params['user_id'] = $this->userId;

        $result = VideoCallLogic::endCall($params);
        if (false === $result) {
            return $this->fail(VideoCallLogic::getError());
        }
        return $this->success('通话结束成功', $result,0,1);
    }


}
