# UserBalance 模型优化

## 问题描述

在使用 `UserBalance::addIncome()` 方法时遇到两个问题：

1. **类型错误**：`inc()` 方法期望 `float` 类型参数，但传入了 `string` 类型
2. **参数重复**：调用时多个参数值相同，代码冗余且容易出错

## 解决方案

### 1. 修复类型错误

在所有涉及数值计算的方法中添加类型转换：

```php
// 确保参数都是数值类型
$amount = (float)$amount;
$commission_income = (float)$commission_income;
// ... 其他参数
```

### 2. 添加便捷方法

为常见的收益类型添加专用方法：

#### addGiftIncome() - 礼物收益
```php
UserBalance::addGiftIncome($userId, $amount);
// 等价于
UserBalance::addIncome($userId, $amount, 0, $amount, $amount, 0);
```

#### addCommissionIncome() - 佣金收益
```php
UserBalance::addCommissionIncome($userId, $amount);
// 等价于
UserBalance::addIncome($userId, $amount, $amount, $amount, 0, 0);
```

#### addVideoIncome() - 视频收益
```php
UserBalance::addVideoIncome($userId, $amount);
// 等价于
UserBalance::addIncome($userId, $amount, 0, $amount, 0, $amount);
```

## 修改文件

### 1. app/common/model/user/UserBalance.php

- 修复了 `addBalance()`、`subBalance()`、`addIncome()` 方法的类型转换
- 新增了三个便捷方法：`addGiftIncome()`、`addCommissionIncome()`、`addVideoIncome()`

### 2. app/applent/logic/gift/GiftLogic.php

优化前：
```php
// 冗余且容易出错
UserBalance::addIncome($userId, $amount, '', $amount, $amount);
UserBalance::addIncome($inviterId, $commissionAmount, $commissionAmount, $commissionAmount);
```

优化后：
```php
// 简洁明确
UserBalance::addGiftIncome($userId, $amount);
UserBalance::addCommissionIncome($inviterId, $commissionAmount);
```

## 优化效果

### 1. 代码可读性提升
- 方法名直接表达业务意图
- 减少参数传递错误的可能性

### 2. 维护性提升
- 统一的类型处理逻辑
- 便于后续扩展和修改

### 3. 错误减少
- 自动类型转换避免类型错误
- 专用方法减少参数传递错误

## 使用建议

### 推荐使用便捷方法
```php
// ✅ 推荐：语义明确
UserBalance::addGiftIncome($userId, $giftAmount);
UserBalance::addCommissionIncome($userId, $commissionAmount);
UserBalance::addVideoIncome($userId, $videoAmount);
```

### 复杂场景使用原方法
```php
// ✅ 复杂场景：需要同时设置多种收益类型
UserBalance::addIncome($userId, $totalAmount, $commission, $total, $gift, $video);
```

## 兼容性

- 所有原有方法保持不变
- 新增方法不影响现有功能
- 向后完全兼容

## 测试建议

1. 测试类型转换是否正确处理字符串参数
2. 验证便捷方法的收益分类是否正确
3. 确认数据库记录的各项收益字段值正确
