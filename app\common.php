<?php
// 应用公共文件
use app\common\service\FileService;
use think\helper\Str;
use app\common\model\user\User;
use app\common\model\user\UserInviteCode;
use app\common\model\user\UserInviteRecord;
use app\common\model\user\UserRebateRatio;
use app\common\service\ConfigService;

/**
 * @notes 生成密码加密密钥
 * @param string $plaintext
 * @param string $salt
 * @return string
 */
function create_password(string $plaintext, string $salt) : string
{
    return md5($salt . md5($plaintext . $salt));
}


/**
 * @notes 随机生成token值
 * @param string $extra
 * @return string
 */
function create_token(string $extra = '') : string
{ 
    $salt = env('project.unique_identification', 'likeadmin');
    $encryptSalt = md5( $salt . uniqid());
    return md5($salt . $extra . time() . $encryptSalt);
}


/**
 * @notes 截取某字符字符串
 * @param $str
 * @param string $symbol
 * @return string
 */
function substr_symbol_behind($str, $symbol = '.') : string
{
    $result = strripos($str, $symbol);
    if ($result === false) {
        return $str;
    }
    return substr($str, $result + 1);
}


/**
 * @notes 对比php版本
 * @param string $version
 * @return bool
 */
function compare_php(string $version) : bool
{
    return version_compare(PHP_VERSION, $version) >= 0 ? true : false;
}


/**
 * @notes 检查文件是否可写
 * @param string $dir
 * @return bool
 */
function check_dir_write(string $dir = '') : bool
{
    $route = root_path() . '/' . $dir;
    return is_writable($route);
}


/**
 * 多级线性结构排序
 * 转换前：
 * [{"id":1,"pid":0,"name":"a"},{"id":2,"pid":0,"name":"b"},{"id":3,"pid":1,"name":"c"},
 * {"id":4,"pid":2,"name":"d"},{"id":5,"pid":4,"name":"e"},{"id":6,"pid":5,"name":"f"},
 * {"id":7,"pid":3,"name":"g"}]
 * 转换后：
 * [{"id":1,"pid":0,"name":"a","level":1},{"id":3,"pid":1,"name":"c","level":2},{"id":7,"pid":3,"name":"g","level":3},
 * {"id":2,"pid":0,"name":"b","level":1},{"id":4,"pid":2,"name":"d","level":2},{"id":5,"pid":4,"name":"e","level":3},
 * {"id":6,"pid":5,"name":"f","level":4}]
 * @param array $data 线性结构数组
 * @param string $symbol 名称前面加符号
 * @param string $name 名称
 * @param string $id_name 数组id名
 * @param string $parent_id_name 数组祖先id名
 * @param int $level 此值请勿给参数
 * @param int $parent_id 此值请勿给参数
 * @return array
 */
function linear_to_tree($data, $sub_key_name = 'sub', $id_name = 'id', $parent_id_name = 'pid', $parent_id = 0)
{
    $tree = [];
    foreach ($data as $row) {
        if ($row[$parent_id_name] == $parent_id) {
            $temp = $row;
            $child = linear_to_tree($data, $sub_key_name, $id_name, $parent_id_name, $row[$id_name]);
            if ($child) {
                $temp[$sub_key_name] = $child;
            }
            $tree[] = $temp;
        }
    }
    return $tree;
}


/**
 * @notes 删除目标目录
 * @param $path
 * @param $delDir
 * @return bool|void
 */
function del_target_dir($path, $delDir)
{
    //没找到，不处理
    if (!file_exists($path)) {
        return false;
    }

    //打开目录句柄
    $handle = opendir($path);
    if ($handle) {
        while (false !== ($item = readdir($handle))) {
            if ($item != "." && $item != "..") {
                if (is_dir("$path/$item")) {
                    del_target_dir("$path/$item", $delDir);
                } else {
                    unlink("$path/$item");
                }
            }
        }
        closedir($handle);
        if ($delDir) {
            return rmdir($path);
        }
    } else {
        if (file_exists($path)) {
            return unlink($path);
        }
        return false;
    }
}


/**
 * @notes 下载文件
 * @param $url
 * @param $saveDir
 * @param $fileName
 * @return string
 */
function download_file($url, $saveDir, $fileName)
{
    if (!file_exists($saveDir)) {
        mkdir($saveDir, 0775, true);
    }
    $fileSrc = $saveDir . $fileName;
    file_exists($fileSrc) && unlink($fileSrc);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
    $file = curl_exec($ch);
    curl_close($ch);
    $resource = fopen($fileSrc, 'a');
    fwrite($resource, $file);
    fclose($resource);
    if (filesize($fileSrc) == 0) {
        unlink($fileSrc);
        return '';
    }
    return $fileSrc;
}


/**
 * @notes 去除内容图片域名
 * @param $content
 * @return array|string|string[]
 */
function clear_file_domain($content)
{
    $fileUrl = FileService::getFileUrl();
    $pattern = '/<img[^>]*\bsrc=["\']'.preg_quote($fileUrl, '/').'([^"\']+)["\']/i';
    return preg_replace($pattern, '<img src="$1"', $content);
}

/**
 * @notes 设置内容图片域名
 * @param $content
 * @return array|string|string[]|null
 */
function get_file_domain($content)
{
    $fileUrl = FileService::getFileUrl();
    $imgPreg = '/(<img .*?src=")(?!https?:\/\/)([^"]*)(".*?>)/is';
    $videoPreg = '/(<video .*?src=")(?!https?:\/\/)([^"]*)(".*?>)/is';
    $content = preg_replace($imgPreg, "\${1}$fileUrl\${2}\${3}", $content);
    $content = preg_replace($videoPreg, "\${1}$fileUrl\${2}\${3}", $content);
    return $content;
}

/**
 * @notes uri小写
 * @param $data
 * @return array|string[]
 */
function lower_uri($data)
{
    if (!is_array($data)) {
        $data = [$data];
    }
    return array_map(function ($item) {
        return strtolower(Str::camel($item));
    }, $data);
}


/**
 * @notes 获取无前缀数据表名
 * @param $tableName
 * @return mixed|string
 */
function get_no_prefix_table_name($tableName)
{
    $tablePrefix = config('database.connections.mysql.prefix');
    $prefixIndex = strpos($tableName, $tablePrefix);
    if ($prefixIndex !== 0 || $prefixIndex === false) {
        return $tableName;
    }
    $tableName = substr_replace($tableName, '', 0, strlen($tablePrefix));
    return trim($tableName);
}


/**
 * @notes 生成编码
 * @param $table
 * @param $field
 * @param string $prefix
 * @param int $randSuffixLength
 * @param array $pool
 * @return string
 */
function generate_sn($table, $field, $prefix = '', $randSuffixLength = 4, $pool = []) : string
{
    $suffix = '';
    for ($i = 0; $i < $randSuffixLength; $i++) {
        if (empty($pool)) {
            $suffix .= rand(0, 9);
        } else {
            $suffix .= $pool[array_rand($pool)];
        }
    }
    $sn = $prefix . date('YmdHis') . $suffix;
    if (app()->make($table)->where($field, $sn)->find()) {
        return generate_sn($table, $field, $prefix, $randSuffixLength, $pool);
    }
    return $sn;
}


/**
 * @notes 格式化金额
 * @param $float
 * @return int|mixed|string
 */
function format_amount($float)
{
    if ($float == intval($float)) {
        return intval($float);
    } elseif ($float == sprintf('%.1f', $float)) {
        return sprintf('%.1f', $float);
    }
    return $float;
}

/**
 * @notes 解密
 * @param $float
 * @return int|mixed|string
 */
function decrypt($base64Data)
{
    // 尝试第一种解密方式（直接解密）
    $result = decryptWithMethod($base64Data, false);

    // 如果第一种方式失败，尝试第二种方式（URL解码和字符替换）
    if ($result === false) {
        $result = decryptWithMethod($base64Data, true);
    }

    return $result;
}

/**
 * @notes 使用指定方法解密
 * @param string $base64Data 待解密的数据
 * @param bool $useUrlDecode 是否使用URL解码和字符替换
 * @return string|false 解密结果或false
 */
function decryptWithMethod($base64Data, $useUrlDecode = false)
{
    try {
        // 如果需要，先进行URL解码和字符替换
        if ($useUrlDecode) {
            $base64Data = strtr(urldecode($base64Data), '-_', '+/');
        }

        // Base64解码
        $rawData = base64_decode($base64Data);

        // 检查解码是否成功
        if ($rawData === false) {
            return false;
        }

        // 分离IV和加密数据
        $iv = substr($rawData, 0, 16);
        $encryptedData = substr($rawData, 16);

        // 如果IV不到16字节，返回失败
        if (strlen($iv) != 16) {
            return false;
        }

        // 生成密钥
        $key = 'wTbn4Wc29qFk62vJlBTmjeaCE1F3KNuo';

        // 执行解密
        $decrypted = openssl_decrypt(
            $encryptedData,
            'AES-256-CBC',
            $key,
            OPENSSL_RAW_DATA,
            $iv
        );

        // 检查解密是否成功
        if ($decrypted === false) {
            return false;
        }

        return trim($decrypted);

    } catch (Exception $e) {
        // 解密过程中出现异常，返回失败
        return false;
    }
}

/**
 * @notes 生成原始密钥
 * @param $float
 * @return int|mixed|string
 */
function generateSecretKey($secretKey) 
{
    // 直接补全到32字节（与Android一致）
    $keyLength = 32;
    $defaultValue = "0";
    
    $strLen = strlen($secretKey);
    if ($strLen < $keyLength) {
        $secretKey = str_pad($secretKey, $keyLength, $defaultValue);
    }
    return substr($secretKey, 0, $keyLength);
}

/**
 * @notes 生成邀请码
 * @param int|null $userId 用户ID，如果提供则为该用户生成邀请码
 * @return string 返回邀请码
 */
function create_invite_code()
{
    do {
        $code = rand_str(6);
        $exists = UserInviteCode::where('invite_code', $code)->findOrEmpty();
    } while (!$exists->isEmpty());

    return $code;
}


/**
 * @notes 生成随机字符串
 * @param $float
 * @return int|mixed|string
 */
function rand_str($len = 8)
{
    $chars = [
        "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k",
        "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v",
        "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G",
        "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
        "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2",
        "3", "4", "5", "6", "7", "8", "9"
    ];
    $charsLen = count($chars) - 1;
    shuffle($chars);    // 将数组打乱
    $output = "";
    for ($i = 0; $i < $len; $i++) {
        $output .= $chars[mt_rand(0, $charsLen)];
    }
    return $output;
}

/**
 * @notes 获取星座
 * @param $birthday
 * @return string
 */
function get_constellation($birthday)
{
    $birthday = strtotime($birthday);
    $month = date('m', $birthday);
    $day = date('d', $birthday);
    $constellation = '';
    // 检查参数有效性
    if ($month < 1 || $month > 12 || $day < 1 || $day > 31) {
        return $constellation;
    }

    if (($month == 1 && $day >= 20) || ($month == 2 && $day <= 18)) {
        $constellation = '水瓶座'; //水瓶
    } else if (($month == 2 && $day >= 19) || ($month == 3 && $day <= 20)) {
        $constellation = '双鱼座'; //双鱼
    } else if (($month == 3 && $day >= 21) || ($month == 4 && $day <= 19)) {
        $constellation = '白羊座'; //白羊
    } else if (($month == 4 && $day >= 20) || ($month == 5 && $day <= 20)) {
        $constellation = '金牛座'; //金牛
    } else if (($month == 5 && $day >= 21) || ($month == 6 && $day <= 21)) {
        $constellation = '双子座'; //双子
    } else if (($month == 6 && $day >= 22) || ($month == 7 && $day <= 22)) {
        $constellation = '巨蟹座'; //巨蟹
    } else if (($month == 7 && $day >= 23) || ($month == 8 && $day <= 22)) {
        $constellation = '狮子座'; //狮子
    } else if (($month == 8 && $day >= 23) || ($month == 9 && $day <= 22)) {
        $constellation = '处女座'; //处女
    } else if (($month == 9 && $day >= 23) || ($month == 10 && $day <= 23)) {
        $constellation = '天平座'; //天秤
    } else if (($month == 10 && $day >= 24) || ($month == 11 && $day <= 22)) {
        $constellation = '天蝎座'; //天蝎
    } else if (($month == 11 && $day >= 23) || ($month == 12 && $day <= 21)) {
        $constellation = '射手座'; //; //射手
    } else if (($month == 12 && $day >= 22) || ($month == 1 && $day <= 19)) {
        $constellation = '摩羯座'; //; //摩羯
    }

    return $constellation;
}

/**
 * @notes 根据类型获取时间范围
 * @param string $type
 * @return array
 */
function getTimeRange(string $type)
{
    $now = time();

    switch ($type) {
        case 'day':
            // 今日 00:00:00 到 23:59:59
            $start = strtotime(date('Y-m-d 00:00:00', $now));
            $end = strtotime(date('Y-m-d 23:59:59', $now));
            break;

        case 'week':
            // 本周一 00:00:00 到本周日 23:59:59
            $weekStart = strtotime('this week Monday', $now);
            $start = strtotime(date('Y-m-d 00:00:00', $weekStart));
            $end = strtotime(date('Y-m-d 23:59:59', strtotime('this week Sunday', $now)));
            break;

        case 'month':
            // 本月1号 00:00:00 到本月最后一天 23:59:59
            $start = strtotime(date('Y-m-01 00:00:00', $now));
            $end = strtotime(date('Y-m-t 23:59:59', $now));
            break;

        default:
            $start = strtotime(date('Y-m-d 00:00:00', $now));
            $end = strtotime(date('Y-m-d 23:59:59', $now));
    }

    return [
        'start' => $start,
        'end' => $end
    ];
}

/**
 * @notes 计算年龄
 * @param string $birthday
 * @return int
 */
function calculateAge($birthday)
{
    $birthDate = new \DateTime($birthday);
    $today = new \DateTime('today');

    // 处理无效日期格式
    if (!$birthDate || $birthDate > $today) {
        return 0;
    }

    return $birthDate->diff($today)->y;
}
/**
 * @notes 生成订单号
 * @param string $type 订单类型
 * @return string
 */
function order_sn($type){
    $order_sn = $type.date('YmdHi').substr(implode('', array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
    return $order_sn;
}

/**
 * @notes 计算用户返佣
 * @param int $userId 用户ID
 * @param float $amount 金额
 * @param int $type 类型 1=充值 2=礼物 3=音频
 * @return array 返回一级和二级用户的返佣信息
 */
function calculate_user_commission($userId, $amount, $type)
{
    try {
        // 初始化返回结果
        $result = [];
        // 检查参数是否合法
        if(!in_array($type, [1,2,3]) || !$userId || !$amount){
            return $result;
        }

        // 查找一级邀请人
        $level1Record = UserInviteRecord::where('invite_user_id', $userId)->find();
        if (!$level1Record) {
            return $result; // 没有邀请人，直接返回
        }

        // 获取一级用户信息
        $level1User = User::where('id', $level1Record->user_id)->find();
        if (!$level1User) {
            return $result;
        }

        // 计算一级返佣
        $level1Rate = getUserRebateRate($level1User, $type, 1); // 1表示一级
        if ($level1Rate > 0) {
            $level1Commission = $amount * ($level1Rate / 100);
            // 如果是充值类型，返佣金额需要×10
            if ($type == 1) {
                $level1Commission = $level1Commission * 10;
            }
            $result['level1'] = [
                'user_id' => $level1Record->user_id,
                'commission' => round($level1Commission, 2),
                'rate' => $level1Rate
            ];
        }

        // 检查是否开启二级返佣
        $enableLevel2Rebate = ConfigService::get('systemconfig', 'enable_two_rebate', 0);
        if ($enableLevel2Rebate) {
            // 查找二级邀请人
            $level2Record = UserInviteRecord::where('invite_user_id', $level1Record->user_id)->find();
            if ($level2Record) {
                // 获取二级用户信息
                $level2User = User::where('id', $level2Record->user_id)->find();
                if ($level2User) {
                    // 计算二级返佣
                    $level2Rate = getUserRebateRate($level2User, $type, 2); // 2表示二级
                    if ($level2Rate > 0) {
                        $level2Commission = $amount * ($level2Rate / 100);
                        // 如果是充值类型，返佣金额需要×10
                        if ($type == 1) {
                            $level2Commission = $level2Commission * 10;
                        }
                        $result['level2'] = [
                            'user_id' => $level2Record->user_id,
                            'commission' => round($level2Commission, 2),
                            'rate' => $level2Rate
                        ];
                    }
                }
            }
        }

        return $result;

    } catch (\Exception $e) {
        // 异常情况返回空结果
        return [];
    }
}

/**
 * 获取用户返佣比例 guoxu
 * @param object $user 用户对象
 * @param int $type 类型 1=充值 2=礼物 3=音频
 * @param int $level 级别 1=一级 2=二级
 * @return float 返佣比例（百分比）
 */
function getUserRebateRate($user, $type, $level)
{
    try {
        // 检查用户是否开启一级返佣
        if ($level == 1 && $user->is_one_rake_back == 1) {
            // 开启，从user_rebate_ratio表获取返佣比例
            $rebateRatio = UserRebateRatio::where('user_id', $user->id)->find();

            if ($rebateRatio) {
                // 根据类型和级别获取对应的返佣比例字段
                $fieldName = getRebateRatioFieldName($type, $level);
                return floatval($rebateRatio[$fieldName] ?? 0);
            }else{
                return 0;
            }
        }

        // 检查用户是否开启二级返佣
        if ($level == 2 && $user->is_two_rake_back == 1) {
            // 开启，从user_rebate_ratio表获取返佣比例
            $rebateRatio = UserRebateRatio::where('user_id', $user->id)->find();

            if ($rebateRatio) {
                // 根据类型和级别获取对应的返佣比例字段
                $fieldName = getRebateRatioFieldName($type, $level);
                return floatval($rebateRatio[$fieldName] ?? 0);
            }else{
                return 0;
            }
        }

        // 非特殊用户或没有个性化配置，使用系统配置
        $configName = getRebateRatioFieldName($type, $level);
        return floatval(ConfigService::get('systemconfig', $configName, 0));

    } catch (\Exception $e) {
        return 0;
    }
}

/**
 * 获取返佣比例字段名
 * @param int $type 类型 1=充值 2=礼物 3=音频
 * @param int $level 级别 1=一级 2=二级
 * @return string 字段名
 */
function getRebateRatioFieldName($type, $level)
{
    $prefix = $level == 1 ? 'one_' : 'two_';

    switch ($type) {
        case 1: // 充值
            return $prefix . 'recharge_rebate_ratio';
        case 2: // 礼物
            return $prefix . 'gift_rebate_ratio';
        case 3: // 音频
            return $prefix . 'audio_rebate_ratio';
        default:
            return $prefix . 'recharge_rebate_ratio';
    }
}

/**
 * @param int $type 1生成昵称，2生成姓名
 * //汉语 - 给用户自动生成昵称
 */
function nickname(){
    /**
     * 随机昵称 形容词
     */
    $nicheng_tou=['迷你的','鲜艳的','飞快的','真实的','清新的','幸福的','可耐的','快乐的','冷静的','醉熏的','潇洒的','糊涂的','积极的','冷酷的','深情的','粗暴的',
        '温柔的','可爱的','愉快的','义气的','认真的','威武的','帅气的','传统的','潇洒的','漂亮的','自然的','专一的','听话的','昏睡的','狂野的','等待的','搞怪的',
        '幽默的','魁梧的','活泼的','开心的','高兴的','超帅的','留胡子的','坦率的','直率的','轻松的','痴情的','完美的','精明的','无聊的','有魅力的','丰富的','繁荣的',
        '饱满的','炙热的','暴躁的','碧蓝的','俊逸的','英勇的','健忘的','故意的','无心的','土豪的','朴实的','兴奋的','幸福的','淡定的','不安的','阔达的','孤独的',
        '独特的','疯狂的','时尚的','落后的','风趣的','忧伤的','大胆的','爱笑的','矮小的','健康的','合适的','玩命的','沉默的','斯文的','香蕉','苹果','鲤鱼','鳗鱼',
        '任性的','细心的','粗心的','大意的','甜甜的','酷酷的','健壮的','英俊的','霸气的','阳光的','默默的','大力的','孝顺的','忧虑的','着急的','紧张的','善良的',
        '凶狠的','害怕的','重要的','危机的','欢喜的','欣慰的','满意的','跳跃的','诚心的','称心的','如意的','怡然的','娇气的','无奈的','无语的','激动的','愤怒的',
        '美好的','感动的','激情的','激昂的','震动的','虚拟的','超级的','寒冷的','精明的','明理的','犹豫的','忧郁的','寂寞的','奋斗的','勤奋的','现代的','过时的',
        '稳重的','热情的','含蓄的','开放的','无辜的','多情的','纯真的','拉长的','热心的','从容的','体贴的','风中的','曾经的','追寻的','儒雅的','优雅的','开朗的',
        '外向的','内向的','清爽的','文艺的','长情的','平常的','单身的','伶俐的','高大的','懦弱的','柔弱的','爱笑的','乐观的','耍酷的','酷炫的','神勇的','年轻的',
        '唠叨的','瘦瘦的','无情的','包容的','顺心的','畅快的','舒适的','靓丽的','负责的','背后的','简单的','谦让的','彩色的','缥缈的','欢呼的','生动的','复杂的',
        '慈祥的','仁爱的','魔幻的','虚幻的','淡然的','受伤的','雪白的','高高的','糟糕的','顺利的','闪闪的','羞涩的','缓慢的','迅速的','优秀的','聪明的','含糊的',
        '俏皮的','淡淡的','坚强的','平淡的','欣喜的','能干的','灵巧的','友好的','机智的','机灵的','正直的','谨慎的','俭朴的','殷勤的','虚心的','辛勤的','自觉的',
        '无私的','无限的','踏实的','老实的','现实的','可靠的','务实的','拼搏的','个性的','粗犷的','活力的','成就的','勤劳的','单纯的','落寞的','朴素的','悲凉的',
        '忧心的','洁净的','清秀的','自由的','小巧的','单薄的','贪玩的','刻苦的','干净的','壮观的','和谐的','文静的','调皮的','害羞的','安详的','自信的','端庄的',
        '坚定的','美满的','舒心的','温暖的','专注的','勤恳的','美丽的','腼腆的','优美的','甜美的','甜蜜的','整齐的','动人的','典雅的','尊敬的','舒服的','妩媚的',
        '秀丽的','喜悦的','甜美的','彪壮的','强健的','大方的','俊秀的','聪慧的','迷人的','陶醉的','悦耳的','动听的','明亮的','结实的','魁梧的','标致的','清脆的',
        '敏感的','光亮的','大气的','老迟到的','知性的','冷傲的','呆萌的','野性的','隐形的','笑点低的','微笑的','笨笨的','难过的','沉静的','火星上的','失眠的',
        '安静的','纯情的','要减肥的','迷路的','烂漫的','哭泣的','贤惠的','苗条的','温婉的','发嗲的','会撒娇的','贪玩的','执着的','眯眯眼的','花痴的','想人陪的',
        '眼睛大的','高贵的','傲娇的','心灵美的','爱撒娇的','细腻的','天真的','怕黑的','感性的','飘逸的','怕孤独的','忐忑的','高挑的','傻傻的','冷艳的','爱听歌的',
        '还单身的','怕孤单的','懵懂的'];
    $nicheng_wei=['凉面','便当','毛豆','花生','可乐','灯泡','野狼','背包','眼神','缘分','雪碧','人生','牛排',
        '蚂蚁','飞鸟','灰狼','斑马','汉堡','悟空','巨人','绿茶','大碗','墨镜','魔镜','煎饼','月饼','月亮','星星','芝麻','啤酒','玫瑰',
        '大叔','小伙','太阳','树叶','芹菜','黄蜂','蜜粉','蜜蜂','信封','西装','外套','裙子','大象','猫咪','母鸡','路灯','蓝天','白云',
        '星月','彩虹','微笑','摩托','板栗','高山','大地','大树','电灯胆','砖头','楼房','水池','鸡翅','蜻蜓','红牛','咖啡','机器猫','枕头','大船','诺言',
        '钢笔','刺猬','天空','飞机','大炮','冬天','洋葱','春天','夏天','秋天','冬日','航空','毛衣','豌豆','黑米','玉米','眼睛','老鼠','白羊','帅哥','美女',
        '季节','鲜花','服饰','裙子','白开水','秀发','大山','火车','汽车','歌曲','舞蹈','老师','导师','方盒','大米','麦片','水杯','水壶','手套','鞋子','自行车',
        '鼠标','手机','电脑','书本','奇迹','身影','香烟','夕阳','台灯','宝贝','未来','皮带','钥匙','心锁','故事','花瓣','滑板','画笔','画板','学姐','店员',
        '电源','饼干','宝马','过客','大白','时光','石头','钻石','河马','犀牛','西牛','绿草','抽屉','柜子','往事','寒风','路人','橘子','耳机','鸵鸟','朋友',
        '苗条','铅笔','钢笔','硬币','热狗','大侠','御姐','萝莉','毛巾','期待','盼望','白昼','黑夜','大门','黑裤','钢铁侠','哑铃','板凳','枫叶','荷花','乌龟',
        '仙人掌','衬衫','大神','草丛','早晨','心情','茉莉','流沙','蜗牛','猎豹','棒球','篮球','乐曲','电话','网络','世界','中心','鱼','鸡','狗',
        '老虎','鸭子','雨','羽毛','翅膀','外套','火','丝袜','书包','钢笔','冷风','八宝粥','烤鸡','大雁','音响','招牌','冰棍','帽子','菠萝','蛋挞','香水',
        '吐司','溪流','黄豆','樱桃','酒窝','紫菜','金鱼','柚子','果汁','项链','煎蛋','唇彩','戒指','雪糕','睫毛','铃铛',
        '手链','香氛','红酒','月光','酸奶','蜡烛','糖豆',
        '薯片','口红','豆芽','发箍','发卡','发夹','发带','铃铛','小笼包','小甜瓜','冬瓜','香菇',
        '短靴','草莓','柠檬','月饼','百合','纸鹤','云朵','芒果','面包','海燕','龙猫','唇膏','鞋垫',
        '羊','黑猫','白猫','金毛','山水','音响','烧鹅','司马','上官','欧阳','夏侯','诸葛','闻人','东方','赫连','皇甫','尉迟','公羊','澹台','公冶','宗政','濮阳','淳于','单于','太叔',
        '申屠','公孙','仲孙','轩辕','令狐','徐离','宇文','长孙','慕容','司徒','司空',];
    /**
     * 百家姓
     */
    $arrXing=['赵','钱','孙','李','周','吴','郑','王','冯','陈','褚','卫','蒋','沈','韩','杨','朱','秦','尤','许','何','吕','施','张','孔','曹','严','华','金','魏','陶','姜','戚','谢','邹',
        '喻','柏','水','窦','章','云','苏','潘','葛','奚','范','彭','郎','鲁','韦','昌','马','苗','凤','花','方','任','袁','柳','鲍','史','唐','费','薛','雷','贺','倪','汤','滕','殷','罗',
        '毕','郝','安','常','傅','卞','齐','元','顾','孟','平','黄','穆','萧','尹','姚','邵','湛','汪','祁','毛','狄','米','伏','成','戴','谈','宋','茅','庞','熊','纪','舒','屈','项','祝',
        '董','梁','杜','阮','蓝','闵','季','贾','路','娄','江','童','颜','郭','梅','盛','林','钟','徐','邱','骆','高','夏','蔡','田','樊','胡','凌','霍','虞','万','支','柯','管','卢','莫',
        '柯','房','裘','缪','解','应','宗','丁','宣','邓','单','杭','洪','包','诸','左','石','崔','吉','龚','程','嵇','邢','裴','陆','荣','翁','荀','于','惠','甄','曲','封','储','仲','伊',
        '宁','仇','甘','武','符','刘','景','詹','龙','叶','幸','司','黎','溥','印','怀','蒲','邰','从','索','赖','卓','屠','池','乔','胥','闻','莘','党','翟','谭','贡','劳','逄','姬','申',
        '扶','堵','冉','宰','雍','桑','寿','通','燕','浦','尚','农','温','别','庄','晏','柴','瞿','阎','连','习','容','向','古','易','廖','庾','终','步','都','耿','满','弘','匡','国','文',
        '寇','广','禄','阙','东','欧','利','师','巩','聂','关','荆','皮'];
    /**
     * 名
     */
    $arrMing=['伟','刚','勇','毅','俊','峰','强','军','平','保','东','文','辉','力','明','永','健','世','广','志','义','兴','良','海','山','仁','波','宁','贵','福','生','龙','元','全'
        ,'国','胜','学','祥','才','发','武','新','利','清','飞','彬','富','顺','信','子','杰','涛','昌','成','康','星','光','天','达','安','岩','中','茂','进','林','有','坚','和','彪','博','诚'
        ,'先','敬','震','振','壮','会','思','群','豪','心','邦','承','乐','绍','功','松','善','厚','庆','磊','民','友','裕','河','哲','江','超','浩','亮','政','谦','亨','奇','固','之','轮','翰'
        ,'朗','伯','宏','言','若','鸣','朋','斌','梁','栋','维','启','克','伦','翔','旭','鹏','泽','晨','辰','士','以','建','家','致','树','炎','德','行','时','泰','盛','雄','琛','钧','冠','策'
        ,'腾','楠','榕','风','航','弘','秀','娟','英','华','慧','巧','美','娜','静','淑','惠','珠','翠','雅','芝','玉','萍','红','娥','玲','芬','芳','燕','彩','春','菊','兰','凤','洁','梅','琳'
        ,'素','云','莲','真','环','雪','荣','爱','妹','霞','香','月','莺','媛','艳','瑞','凡','佳','嘉','琼','勤','珍','贞','莉','桂','娣','叶','璧','璐','娅','琦','晶','妍','茜','秋','珊','莎'
        ,'锦','黛','青','倩','婷','姣','婉','娴','瑾','颖','露','瑶','怡','婵','雁','蓓','纨','仪','荷','丹','蓉','眉','君','琴','蕊','薇','菁','梦','岚','苑','婕','馨','瑗','琰','韵','融','园'
        ,'艺','咏','卿','聪','澜','纯','毓','悦','昭','冰','爽','琬','茗','羽','希','欣','飘','育','滢','馥','筠','柔','竹','霭','凝','晓','欢','霄','枫','芸','菲','寒','伊','亚','宜','可','姬'
        ,'舒','影','荔','枝','丽','阳','妮','宝','贝','初','程','梵','罡','恒','鸿','桦','骅','剑','娇','纪','宽','苛','灵','玛','媚','琪','晴','容','睿','烁','堂','唯','威','韦','雯','苇','萱'
        ,'阅','彦','宇','雨','洋','忠','宗','曼','紫','逸','贤','蝶','菡','绿','蓝','儿','翠','烟'];

}