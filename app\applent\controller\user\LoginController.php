<?php

namespace app\applent\controller\user;

use app\applent\logic\user\LoginLogic;
use app\applent\validate\user\SendSmsValidate;
use app\applent\validate\user\LoginValidate;
use app\applent\validate\user\PerfectInfoValidate;
use app\applent\validate\user\LoginByWechatValidate;
use app\applent\controller\BaseApiController;
/**
 * 登录
 * Class LoginController
 * @package app\api\controller
 */
class LoginController extends BaseApiController
{

    public array $notNeedLogin = ['send_code','mobile_login','login','register'];



    /**
     * @notes 发送短信验证码
     * @return \think\response\Json
     */
    public function send_code()
    {
        $params = (new SendSmsValidate())->post()->goCheck();
        $result = LoginLogic::sendCode($params);
        if ($result) {
            return $this->success('发送成功',$result,1,1);
        }
        return $this->fail(LoginLogic::getError());
    }

    /**
     * @notes 手机号验证码登录
     * @return \think\response\Json
     */
    public function mobile_login()
    {
        $params = (new LoginValidate())->post()->goCheck('code');
        $result = LoginLogic::loginByCode($params);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('登录成功',$result,1,1);
    }

    /**
     * @notes 账号密码注册
     * @return \think\response\Json
     */
    public function register()
    {
        $params = (new LoginValidate())->post()->goCheck('register');
        $result = LoginLogic::registerByCode($params);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('注册成功',$result,1,1);
    }

    /**
     * @notes 手机号密码登录
     * @return \think\response\Json
     */
    public function login()
    {
        $params = (new LoginValidate())->post()->goCheck('password');
        $result = LoginLogic::loginByMobile($params);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('登录成功',$result,1,1);
    }
    /**
     * @notes 微信授权登录
     * @return \think\response\Json
     */
    public function loginByWechat()
    {
        $params = (new LoginValidate())->post()->goCheck();
        $result = LoginLogic::loginByWechat($params);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->data($result);
    }
    /**
     * @notes 完善用户信息
     * @return \think\response\Json
     */
    public function perfect_info()
    {
        $params = (new PerfectInfoValidate())->post()->goCheck();
        $result = LoginLogic::perfectInfo($params,$this->userId);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('完善成功',$result,1,1);
    }

    /**
     * @notes 验证验证码
     * @return \think\response\Json
     */
    public function verify_code()
    {
        $params = (new LoginValidate())->post()->goCheck('verifyCode');
        $result = LoginLogic::verifyCode($params,$this->userId);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('验证成功',[],1,0);
    }

    /**
     * @notes 修改密码
     * @return \think\response\Json
     */
    public function change_password()
    {
        $params = (new LoginValidate())->post()->goCheck('changePassword');
        $result = LoginLogic::changePassword($params,$this->userId);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('修改成功',[],1,1);
    }

    //注销账号
    public function logout_account()
    {
        $params = (new LoginValidate())->post()->goCheck('logoutAccount');
        $result = LoginLogic::logoutAccount($params,$this->userInfo);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('注销成功',[],1,1);
    }

    /**
     * @notes 退出登录
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function logout()
    {
        $result =LoginLogic::logout($this->userInfo);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('退出成功',[],1,1);
    }

    /**
     * @notes 获取随机昵称
     * @return \think\response\Json
     */
    public function get_random_nickname()
    {
        $nickname = nickname();
        return $this->success('获取成功',['nickname'=>$nickname],1,0);
    }
}